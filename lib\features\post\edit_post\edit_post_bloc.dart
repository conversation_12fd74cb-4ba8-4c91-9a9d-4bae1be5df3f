import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/add_image_option/add_image_option_screen.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/upload_post_files_in_background/upload_post_files.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';

enum EditPostScreenState { Loading, Success, Failed, Empty}

class EditPostBloc {
  //region Common variable
  late BuildContext context;
  final PostDetail postDetail;
  List<File> selectedImage = [];
  String reviewCount = "0";
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel ;

  // Mention functionality variables
  final TypingSuggestionsService _typingSuggestionsService = TypingSuggestionsService();
  List<SuggestionItem> suggestions = [];
  List<MentionData> mentions = [];
  bool showSuggestions = false;
  bool isLoadingSuggestions = false;
  bool hasMoreSuggestions = false;
  String currentQuery = '';
  int suggestionsOffset = 0;
  int suggestionsLimit = 10;
  String _encodedText = "";
  Map<int, String> _mentionPositions = {};
  //endregion

//region Text Editing Controller
  MyReachTextController addPostTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(r"@[a-zA-Z0-9_/\-]+"):
        AppTextStyle.access0(textColor: AppColors.brandGreen),
  }, onMatch: (List<String> match) {});
//endregion

//region Controller
  final editUpdatePostScreenStateCtrl = StreamController<EditPostScreenState>.broadcast();
  // final selectedImageCtrl = StreamController<List<PostImages>>.broadcast();
  final refreshCtrl = StreamController<bool>.broadcast();

  // Mention functionality controllers
  final suggestionsCtrl = StreamController<List<SuggestionItem>>.broadcast();
  final showSuggestionsCtrl = StreamController<bool>.broadcast();
  final suggestionsLoadingCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  EditPostBloc(this.context, this.postDetail);
  //endregion
//region Init
  init(){

    //Add review count
    if(postDetail.commentType == CommentEnums.REVIEW.name){
      reviewCount = postDetail.ratingCount.toString();
      refreshCtrl.sink.add(true);
    }

    // Parse existing mentions from the original post text
    _parseExistingMentions(postDetail.text ?? '');

    // Convert encoded mentions to display text for editing
    String displayText = MentionParser.convertToDisplayText(postDetail.text ?? '');
    addPostTextCtrl.text = displayText;

    // Initialize encoded text with original text
    _encodedText = postDetail.text ?? '';

    // Add text change listener for typing suggestions
    addPostTextCtrl.addListener(_onTextChanged);

    // selectedImageCtrl.add(postDetail.postImages!);
    appConfigDataModel = Provider.of<AppConfigDataModel>(context, listen: false);


  }
//endregion








  void onTapAddImage() async{
    int imageLimit = appConfigDataModel.appConfig!.postImageLimit;

    //If web view then open download
    if(kIsWeb){
      return await CommonMethods.appDownloadDialog();
    }

    //Check if image count has reached the limit of 6
    if ((selectedImage.length + postDetail.images!.length ) >= imageLimit) {
      return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
    }


    Widget screen = const AddImageOptionScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // If value is null or postDetail is null or postImages is null
      if (value == null || postDetail == null || postDetail.images == null) {
        return;
      }

      try {
        // Check how many images are being added
        List<File> newImages = (value as List).cast<File>();
        int totalImagesAfterAddition = (selectedImage.length + postDetail.images!.length) + newImages.length;

        // If adding the new images exceeds the limit, show a warning and add only up to the limit
        if (totalImagesAfterAddition > imageLimit) {
          int imagesToAdd = imageLimit - (selectedImage.length + postDetail.images!.length); // Add only the number of images that fit
          // selectedImage.addAll(newImages.take(imagesToAdd).toList());
          selectedImage.insertAll(selectedImage.length, newImages.take(imagesToAdd).toList());

          // Refresh if needed (assuming refreshCtrl is a StreamController)
          refreshCtrl.sink.add(true);
          return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
        } else {
          // Add all selected images if within the limit
          selectedImage.addAll(newImages);
        }

        // Refresh if needed (assuming refreshCtrl is a StreamController)
        refreshCtrl.sink.add(true);

        // Print statement for debugging or confirmation
        //print('Images added successfully');
      } catch (e) {
        //print('Error while adding images: $e');
      }
    });
  }


  //region Remove local image
  void removeLocalImage({required String filePath }){
    //Remove image from the list
    selectedImage.removeWhere((element) => element.path == filePath);
    //Update ui
    refreshCtrl.sink.add(true);

  }
  //endregion

  ///Post
  //region Delete post image
  Future<void>deletePostImage({required String imageId,required String postReference })async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      await PostService().deletePostImage(imageId: imageId, postReference:postReference );

      //Remove image in local
      // postDetail.postImages.removeWhere((element) => element.postImageId == imageId);

      //Remove image from data model
      var post = postDataModel.allPostDetailList.firstWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      post.images!.removeWhere((element) => element.mediaId == imageId);

      //Refresh
      postDataModel.updateUi();
      //Update ui
      refreshCtrl.sink.add(true);


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion


  //region Edit post api call
  Future<void>editPostApiCall()async{
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);

    //Text field empty check
    //Text field empty check and image
    if(postDetail.images!.isEmpty && addPostTextCtrl.text.trim().isEmpty && selectedImage.isEmpty){
      return CommonMethods.toastMessage(AppStrings.emptyPostCanNotBeAdded, context);
    }
    try{
      //Loading
      editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Loading);
      //Add the flag that post is adding
      postDataModel.postingStatus = true;
      //Remove the exact product detail from main list
      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Close screen
      context.mounted?Navigator.pop(context):null;
      //Update ui
      postDataModel.updateUi();

      //Api call
      // Convert display text back to encoded format for API
      String encodedText = _convertDisplayTextToEncodedText(addPostTextCtrl.text);

      // Prepare tagged references
      List<Map<String, dynamic>> taggedReferences = _prepareTaggedReferences();

      await PostAndCommentUploadFiles().editPostApiCall(
        postText: cleanText(encodedText),
        selectedImage: selectedImage.map((e) => e.path).toList(),
        postReference: postDetail.postOrCommentReference!,
        taggedReferences: taggedReferences,
      );
    }
    on ApiErrorResponseMessage catch(error){
      //Add the flag that post is adding
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Add the flag that post is adding
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
  //endregion


  //region Clean text
  String cleanText(String input) {
    // Trim spaces and newlines from start and end
    String sanitized = input.trim();

    // Check if the resulting text starts and ends with non-space characters
    if (sanitized.isNotEmpty &&
        sanitized[0] != ' ' &&
        sanitized[sanitized.length - 1] != ' ') {
      return sanitized;
    }

    // Return an empty string if conditions are not met
    return '';
  }
  //endregion

  //region Parse existing mentions from post text
  void _parseExistingMentions(String originalText) {
    // Clear existing mentions
    mentions.clear();

    // Regex to match encoded mentions
    RegExp mentionRegex = RegExp(r'\{\{mention:\{[^}]*\}\}\}');

    Iterable<RegExpMatch> matches = mentionRegex.allMatches(originalText);

    for (RegExpMatch match in matches) {
      String encodedMention = match.group(0) ?? '';
      try {
        // Parse the encoded mention to extract data
        String jsonPart = encodedMention
            .replaceFirst('{{mention:', '')
            .replaceFirst('}}', '');

        Map<String, dynamic> mentionData = jsonDecode(jsonPart);
        String reference = mentionData['reference'] ?? '';
        String displayText = mentionData['display_text'] ?? '';

        if (reference.isNotEmpty && displayText.isNotEmpty) {
          // Determine mention type based on reference prefix or display text pattern
          String type = _determineMentionType(reference, displayText);

          MentionData mention = MentionData(
            type: type,
            displayText: displayText,
          );

          // Set the appropriate reference based on type
          switch (type) {
            case 'USER':
              mention.userReference = reference;
              break;
            case 'STORE':
              mention.storeReference = reference;
              break;
            case 'PRODUCT':
              mention.productReference = reference;
              // For products, we might need to extract store reference from display text
              if (displayText.contains('/')) {
                String storeHandle = displayText.split('/')[0].replaceFirst('@', '');
                mention.storeReference = storeHandle;
              }
              break;
          }

          mentions.add(mention);
        }
      } catch (e) {
        // Skip invalid mentions
        debugPrint('Error parsing mention: $encodedMention - $e');
      }
    }
  }

  String _determineMentionType(String reference, String displayText) {
    // Try to determine type based on reference prefix
    if (reference.startsWith('U')) {
      return 'USER';
    } else if (reference.startsWith('S')) {
      return 'STORE';
    } else if (reference.startsWith('P')) {
      return 'PRODUCT';
    }

    // Fallback: determine by display text pattern
    if (displayText.contains('/')) {
      return 'PRODUCT';
    } else {
      return 'USER'; // Default to USER
    }
  }

  //region Convert display text to encoded text
  String _convertDisplayTextToEncodedText(String displayText) {
    // Sync the encoded text with current display text and mentions
    _syncEncodedText(displayText);
    return _encodedText.isNotEmpty ? _encodedText : displayText;
  }
  //endregion

  //region Prepare tagged references
  List<Map<String, dynamic>> _prepareTaggedReferences() {
    List<Map<String, dynamic>> taggedReferences = [];
    int order = 1;

    // Add mentions from text field
    for (MentionData mention in mentions) {
      String? reference;
      String? type;

      switch (mention.type) {
        case 'USER':
          reference = mention.userReference;
          type = 'USER';
          break;
        case 'STORE':
          reference = mention.storeReference;
          type = 'STORE';
          break;
        case 'PRODUCT':
          reference = mention.productReference;
          type = 'PRODUCT';
          break;
      }

      if (reference != null && type != null) {
        taggedReferences.add({
          'reference': reference,
          'type': type,
          'order': order++,
        });
      }
    }

    return taggedReferences;
  }
  //endregion


  //region Get single post
  Future<void >getSinglePost({required String postReference})async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      PostDetail postDetail = await PostService().getSinglePost(postReference: postReference);
      //Add updated data into data model
      postDataModel.addPostIntoList(postList: [postDetail]);
      //Add the flag that post is false
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      return;
    }
  }
//endregion


///Comment

  //region Edit comment api call
  Future<void>editCommentApiCall()async{
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);

    //Text field empty check
    //Text field empty check and image
    if(postDetail.images!.isEmpty && addPostTextCtrl.text.trim().isEmpty && selectedImage.isEmpty){
      return CommonMethods.toastMessage(AppStrings.emptyCommentCanNotBeAdded, context);
    }
    try{
      // Loading
      // editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Loading);
      // //Add the flag that post is adding
      // postDataModel.postingStatus = true;
      //Remove the exact product detail from main list
      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Close screen
      context.mounted?Navigator.pop(context):null;
      //Update ui
      postDataModel.updateUi();

      //Api call
      // Convert display text back to encoded format for API
      String encodedText = _convertDisplayTextToEncodedText(addPostTextCtrl.text);

      // Prepare tagged references
      List<Map<String, dynamic>> taggedReferences = _prepareTaggedReferences();

      await PostAndCommentUploadFiles().editCommentApiCall(
        reviewCount:postDetail.commentType == CommentEnums.REVIEW.name?reviewCount:null,
        postText: encodedText,
        selectedImage: selectedImage.map((e) => e.path).toList(),
        commentReference: postDetail.postOrCommentReference!,
        taggedReferences: taggedReferences,
      );
    }
    on ApiErrorResponseMessage catch(error){
      //Add the flag that post is adding
      // postDataModel.postingStatus = false;
      // //Update ui
      // postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Add the flag that post is adding
      // postDataModel.postingStatus = false;
      // //Update ui
      // postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
  //endregion

  //region Delete comment image
  Future<void>deleteCommentImage({required String imageId,required String postOrCommentReference })async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      await PostService().deleteCommentImage(imageId: imageId, postReference:postOrCommentReference );

      //Remove image in local
      // postDetail.postImages.removeWhere((element) => element.postImageId == imageId);

      //Remove image from data model
      var post = postDataModel.allPostDetailList.firstWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      post.images!.removeWhere((element) => element.mediaId == imageId);

      //Refresh
      postDataModel.updateUi();
      //Update ui
      refreshCtrl.sink.add(true);


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion

  //region Typing Suggestions
  void _onTextChanged() {
    String text = addPostTextCtrl.text;
    int cursorPosition = addPostTextCtrl.selection.baseOffset;

    // Check if user is editing an existing mention
    _handleMentionEditing(text, cursorPosition);

    // Find the current word being typed
    String currentWord = _getCurrentWord(text, cursorPosition);

    final isAtMention = currentWord.startsWith('@') && 
                    currentWord.length > 1 && 
                    !currentWord.contains(' ') && 
                    currentWord == currentWord.trim();

    if (isAtMention) {
      String query = currentWord;
      if (query != currentQuery) {
        currentQuery = query;
        suggestionsOffset = 0;
        _fetchSuggestions(query);
      }
    } else {
      _hideSuggestions();
    }
  }

  void _handleMentionEditing(String currentText, int cursorPosition) {
    // Check if any existing mentions have been modified
    List<MentionData> mentionsToRemove = [];

    for (MentionData mention in mentions) {
      if (mention.displayText != null) {
        int mentionIndex = currentText.indexOf(mention.displayText!);

        // If mention is not found or has been modified, remove it
        if (mentionIndex == -1 || _isMentionModified(currentText, mention, mentionIndex)) {
          mentionsToRemove.add(mention);
        } else {
          // Check if cursor is within the mention and text has been partially edited
          int mentionEnd = mentionIndex + mention.displayText!.length;
          if (cursorPosition > mentionIndex && cursorPosition <= mentionEnd) {
            // Check if the mention text at this position has been altered
            String actualText = currentText.substring(mentionIndex,
                math.min(mentionEnd, currentText.length));
            if (actualText != mention.displayText && actualText.startsWith('@')) {
              // User is editing within the mention, remove the entire mention
              mentionsToRemove.add(mention);

              // Also remove the partial mention text from display
              String newText = currentText.replaceRange(mentionIndex,
                  math.min(mentionEnd, currentText.length), '');
              addPostTextCtrl.text = newText;
              addPostTextCtrl.selection = TextSelection.collapsed(offset: mentionIndex);
              return; // Exit early to avoid further processing
            }
          }
        }
      }
    }

    // Remove modified mentions
    for (MentionData mention in mentionsToRemove) {
      mentions.remove(mention);
    }

    // Update encoded text to match current display text
    _syncEncodedText(currentText);
  }

  bool _isMentionModified(String currentText, MentionData mention, int mentionIndex) {
    if (mention.displayText == null) return false;

    int mentionEnd = mentionIndex + mention.displayText!.length;

    // Check if the mention text has been altered
    if (mentionEnd > currentText.length) return true;

    String actualMentionText = currentText.substring(mentionIndex, mentionEnd);
    return actualMentionText != mention.displayText;
  }

  void _syncEncodedText(String displayText) {
    _encodedText = displayText;

    // Replace display mentions with encoded mentions using more precise replacement
    for (MentionData mention in mentions) {
      if (mention.displayText != null) {
        String encodedMention = _createEncodedMentionFromData(mention);
        // Use replaceFirst to avoid issues with similar mentions
        _encodedText = _encodedText.replaceFirst(mention.displayText!, encodedMention);
      }
    }
  }

  String _createEncodedMentionFromData(MentionData mention) {
    String reference = '';
    switch (mention.type) {
      case 'USER':
        reference = mention.userReference ?? '';
        break;
      case 'STORE':
        reference = mention.storeReference ?? '';
        break;
      case 'PRODUCT':
        reference = mention.productReference ?? '';
        break;
    }

    return '{{mention:{"reference":"$reference","display_text":"${mention.displayText}"}}}';
  }

  String _getCurrentWord(String text, int cursorPosition) {
    if (cursorPosition <= 0 || cursorPosition > text.length) return '';

    // Find the start of the current word (look for @)
    int start = cursorPosition - 1;
    while (start > 0 && text[start - 1] != ' ' && text[start - 1] != '\n') {
      start--;
    }

    // If we found an @ symbol, this might be a mention
    if (start < text.length && text[start] == '@') {
      // For mentions, we need to handle different patterns:
      // 1. @username (user mentions)
      // 2. @storename (store mentions)
      // 3. @storename/product-slug (product mentions via direct typing)
      // 4. @storename's Product Name (product mentions with apostrophe)

      int end = cursorPosition;
      bool foundSlash = false;
      bool foundApostrophe = false;

      // First, find the end of the basic mention (until space, slash, or apostrophe)
      while (end < text.length && text[end] != ' ' && text[end] != '\n') {
        if (text[end] == '/') {
          foundSlash = true;
          end++; // Include the slash
          break;
        }
        if (text[end] == "'") {
          foundApostrophe = true;
        }
        end++;
      }

      // If we found a slash, continue to get the product slug part
      if (foundSlash) {
        // Continue until we find a space or newline (for product slug)
        while (end < text.length && text[end] != ' ' && text[end] != '\n') {
          end++;
        }
      }
      // If we found an apostrophe and there's more text, this might be a product mention
      else if (foundApostrophe && end < text.length - 1 && text.substring(start, end).contains("'s")) {
        // Continue until we find a space or newline that's not part of the product name
        while (end < text.length && text[end] != '\n') {
          if (text[end] == ' ') {
            // Check if this space is followed by another mention or end of text
            if (end + 1 >= text.length || text[end + 1] == '@' || text[end + 1] == '\n') {
              break;
            }
          }
          end++;
        }
      }

      return text.substring(start, end);
    } else {
      // For non-mentions, use the original logic
      int end = cursorPosition;
      while (end < text.length && text[end] != ' ' && text[end] != '\n') {
        end++;
      }
      return text.substring(start, end);
    }
  }

  Future<void> _fetchSuggestions(String query, {bool loadMore = false}) async {
    if (isLoadingSuggestions) return;

    isLoadingSuggestions = true;
    suggestionsLoadingCtrl.sink.add(true);

    if (!loadMore) {
      suggestions.clear();
      suggestionsOffset = 0;
    }

    try {
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      TypingSuggestionsResponse response =
          await _typingSuggestionsService.getTypingSuggestions(
        query: query,
        limit: suggestionsLimit,
        offset: suggestionsOffset,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      if (loadMore) {
        suggestions.addAll(response.results ?? []);
      } else {
        suggestions = response.results ?? [];
      }

      hasMoreSuggestions = (response.results?.length ?? 0) == suggestionsLimit;
      suggestionsOffset += suggestionsLimit;

      showSuggestions = suggestions.isNotEmpty;
      suggestionsCtrl.sink.add(suggestions);
      showSuggestionsCtrl.sink.add(showSuggestions);
    } catch (e) {
      // Handle error silently for typing suggestions
      showSuggestions = false;
      showSuggestionsCtrl.sink.add(false);
    } finally {
      isLoadingSuggestions = false;
      suggestionsLoadingCtrl.sink.add(false);
    }
  }

  void _hideSuggestions() {
    showSuggestions = false;
    suggestions.clear();
    currentQuery = '';
    showSuggestionsCtrl.sink.add(false);
    suggestionsCtrl.sink.add([]);
  }

  void loadMoreSuggestions() {
    if (hasMoreSuggestions &&
        !isLoadingSuggestions &&
        currentQuery.isNotEmpty) {
      _fetchSuggestions(currentQuery, loadMore: true);
    }
  }

  void onSuggestionTap(SuggestionItem suggestion) {
    _insertMention(suggestion);
  }

  void _insertMention(SuggestionItem suggestion) {
    String text = addPostTextCtrl.text;
    int cursorPosition = addPostTextCtrl.selection.baseOffset;

    // Use the same logic as _getCurrentWord to find the mention boundaries
    String currentMention = _getCurrentWord(text, cursorPosition);

    // Find the actual start position of the mention
    int start = text.lastIndexOf(currentMention, cursorPosition - 1);
    if (start == -1) {
      // Fallback to original logic
      start = cursorPosition - 1;
      while (start > 0 && text[start - 1] != ' ' && text[start - 1] != '\n') {
        start--;
      }
    }

    int end = start + currentMention.length;

    // Create display text and encoded mention
    String displayText = _createDisplayText(suggestion);
    String encodedMention = _createMentionText(suggestion);

    // Replace the current @mention with display text only
    String newText = text.replaceRange(start, end, displayText);

    // Store mention data
    MentionData mentionData = MentionData(
      type: suggestion.type,
      displayText: displayText,
    );

    switch (suggestion.type) {
      case 'USER':
        mentionData.userReference = suggestion.reference;
        break;
      case 'STORE':
        mentionData.storeReference = suggestion.reference;
        break;
      case 'PRODUCT':
        mentionData.productReference = suggestion.reference;
        mentionData.storeReference =
            suggestion.storeHandle; // Store handle for products
        break;
    }

    mentions.add(mentionData);

    // Update the encoded text with the full mention format
    _updateEncodedText(newText, start, displayText, encodedMention);

    // Update text and cursor position
    addPostTextCtrl.text = newText;
    int newCursorPosition = start + displayText.length;
    addPostTextCtrl.selection =
        TextSelection.collapsed(offset: newCursorPosition);

    // Hide suggestions
    _hideSuggestions();
  }

  String _createDisplayText(SuggestionItem suggestion) {
    switch (suggestion.type) {
      case 'USER':
        return '@${suggestion.primaryText}';
      case 'STORE':
        return '@${suggestion.primaryText}';
      case 'PRODUCT':
        return '@${suggestion.storeHandle}/${suggestion.primaryText}';
      default:
        return '@${suggestion.primaryText}';
    }
  }

  String _createMentionText(SuggestionItem suggestion) {
    switch (suggestion.type) {
      case 'USER':
        return '{{mention:{"reference":"${suggestion.reference ?? ''}","display_text":"@${suggestion.primaryText}"}}}';
      case 'STORE':
        return '{{mention:{"reference":"${suggestion.reference ?? ''}","display_text":"@${suggestion.primaryText}"}}}';
      case 'PRODUCT':
        return '{{mention:{"reference":"${suggestion.reference ?? ''}","display_text":"@${suggestion.storeHandle}/${suggestion.primaryText}"}}}';
      default:
        return '{{mention:{"reference":"${suggestion.reference ?? ''}","display_text":"@${suggestion.primaryText}"}}}';
    }
  }

  void _updateEncodedText(String displayText, int mentionStart, String displayMention, String encodedMention) {
    // Update the encoded text by replacing display text with encoded mention
    _encodedText = displayText.replaceRange(mentionStart, mentionStart + displayMention.length, encodedMention);

    // Store mention position for tracking
    _mentionPositions[mentionStart] = encodedMention;
  }

  String getEncodedTextForAPI() {
    // Sync the encoded text with current display text before returning
    _syncEncodedText(addPostTextCtrl.text);
    return _encodedText.isNotEmpty ? _encodedText : addPostTextCtrl.text;
  }
  //endregion


//region Dispose
  void dispose(){
    editUpdatePostScreenStateCtrl.close();
    refreshCtrl.close();
    suggestionsCtrl.close();
    showSuggestionsCtrl.close();
    suggestionsLoadingCtrl.close();
  }
//endregion
}