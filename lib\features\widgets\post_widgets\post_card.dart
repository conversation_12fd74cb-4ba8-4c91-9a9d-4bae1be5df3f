import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_and_product_appbar/post_and_product_appbar.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/tagged_items_widget/tagged_items_widget.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/add_visited_reference/add_visited_references.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_common_image_icon/app_common_image_icon.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:visibility_detector/visibility_detector.dart';

class PostCard extends StatefulWidget {
  final PostDetail postDetail;
  final Function onTapEdit;
  final Function onTapDelete;
  final Function? onTapReport;
  final Function onTapHeart;
  final Function onTapDrawer;
  final Function onTapProfileImage;
  final Function onTapShare;
  final Function onTapPost;
  final bool? isFullView;
  final String? customTitle;
  final bool? isCustomTitleVisible;
  final double imageSize;
  final bool isFromSinglePost;

  const PostCard(
      {super.key,
      required this.postDetail,
      required this.onTapEdit,
      required this.onTapDelete,
      this.onTapReport,
      required this.onTapHeart,
      required this.onTapDrawer,
      required this.onTapProfileImage,
      required this.onTapShare,
      required this.onTapPost,
      this.imageSize = 236,
      this.isFromSinglePost = false,
      this.isFullView = false,
      this.customTitle,
      this.isCustomTitleVisible = false});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  //region Bloc
  late PostCardBloc postCardBloc;

  //endregion

  //region Init
  @override
  void initState() {
    postCardBloc = PostCardBloc(context, widget.postDetail);
    postCardBloc.init();
    super.initState();
  }

//endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        leadingIcon: AppImages.locationIcon,
        isCustomLeadingIcon: true,
        onTapLeading: () {},
        context: context,
        isCenterTitle: true,
        isCustomTitle: true,
        customTitleWidget: const Text("Hello"),
        isDefaultMenuVisible: false);
  }

  //endregion

  //region Body
  Widget body() {
    return VisibilityDetector(
      key: UniqueKey(),
      onVisibilityChanged: (visibilityInfo) {
        var visiblePercentage = visibilityInfo.visibleFraction * 100;
        if (visiblePercentage > 50) {
          //Add to visited post
          //AddVisitedReferences().addReferences(reference: widget.postDetail.postOrCommentReference!);
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          userInfo(),
          InkWell(
            highlightColor: Colors.transparent,
            onTap: () {
              widget.onTapPost();
            },
            onDoubleTap: () {
              //If already liked then return
              if (widget.postDetail.likeStatus!) {
                return;
              }
              widget.onTapHeart();
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                postImage(),
                comment(),
                taggedItemsDropdown(),
                action(),
                counts(),
              ],
            ),
          ),
        ],
      ),
    );
  }

//endregion

  //region Widget user info
  Widget userInfo() {
    return widget.isCustomTitleVisible! && widget.customTitle != null
        ? repostedPostHeader()
        : simplePostHeader();
    // return Container(
    //   alignment: Alignment.centerLeft,
    //   padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
    //   child: Row(
    //     mainAxisSize: MainAxisSize.min,
    //     mainAxisAlignment: MainAxisAlignment.start,
    //     crossAxisAlignment: CrossAxisAlignment.center,
    //     children: [
    //       InkWell(
    //         onTap: () {
    //           widget.onTapProfileImage();
    //         },
    //         child: CustomImageContainer(
    //           width: 32,
    //           height: 32,
    //           imageUrl: widget.postDetail.createdBy!.icon,
    //           imageType: widget.postDetail.createdBy!.entityType == EntityType.USER.name ? CustomImageContainerType.user : CustomImageContainerType.store,
    //         ),
    //         // child: ClipRRect(
    //         //   borderRadius: BorderRadius.circular(widget.postDetail.entityType == EntityType.USER.name?100:(0.4130 * 27)),
    //         //   child: SizedBox(
    //         //     height: 27,
    //         //     width: 27,
    //         //     child: extendedImage(widget.postDetail.icon, context, 100, 100, customPlaceHolder:widget.postDetail.entityType == EntityType.USER.name?AppImages.userPlaceHolder:AppImages.storePlaceHolder),
    //         //   ),
    //         // ),
    //       ),
    //       const SizedBox(
    //         width: 7,
    //       ),
    //       //Handle and time
    //       Expanded(
    //         child: Column(
    //           mainAxisSize: MainAxisSize.min,
    //           mainAxisAlignment: MainAxisAlignment.start,
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             Flexible(
    //               child: InkWell(
    //                 onTap: () {
    //                   widget.onTapProfileImage();
    //                 },
    //                 child: Text(
    //                   widget.postDetail.createdBy!.handle!,
    //                   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    //                   overflow: TextOverflow.ellipsis,
    //                 ),
    //               ),
    //             ),
    //             const SizedBox(
    //               width: 5,
    //             ),
    //             Text(
    //               postCardBloc.convertDateFormat(inputDateTimeString: widget.postDetail.createdDate!),
    //               overflow: TextOverflow.ellipsis,
    //               style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
    //             ),
    //           ],
    //         ),
    //       ),
    //       //Three dots
    //       SizedBox(
    //           height: 18,
    //           width: 18,
    //           child: CupertinoButton(
    //             padding: EdgeInsets.zero,
    //             onPressed: () {
    //               // widget.onTapDrawer();
    //               postCardBloc.onTapDrawer(postDetail: widget.postDetail);
    //             },
    //             child: RotatedBox(
    //                 quarterTurns: 1,
    //                 child: SvgPicture.asset(
    //                   AppImages.commentOption,
    //                   color: AppColors.appBlack,
    //                   height: 18,
    //                 )),
    //           ))
    //     ],
    //   ),
    // );
  }

  //endregion

  //region Post image
  Widget postImage() {
    return widget.postDetail.images!.isNotEmpty
        ? Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(vertical: 5),
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  height: widget.imageSize,
                  child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: widget.postDetail.images!.length,
                      itemBuilder: (context, index) {
                        return CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              postCardBloc.onTapImage(
                                  index: index,
                                  imageList: widget.postDetail.images!
                                      .map((e) => e.mediaPath!)
                                      .toList());
                            },
                            // child: extendedImage(widget.postDetail.postImages![index].postImage, context, 800, 800),
                            child: PostAndProductImageWidgets(
                              localOrNetworkImage:
                                  widget.postDetail.images![index].mediaPath!,
                              imageSize: widget.imageSize,
                            ));
                      }),
                )))
        : const SizedBox();
  }

  //endregion

  //region Comment
  Widget comment() {
    return Visibility(
      visible:
          widget.postDetail.text != null && widget.postDetail.text!.isNotEmpty,
      child: Container(
          alignment: Alignment.centerLeft,
          // margin: const EdgeInsets.only(left: 15, right: 15, top: 5),
          margin:
              // widget.postDetail.images!.isNotEmpty?
              const EdgeInsets.only(left: 15, right: 15, bottom: 5),
          //
          //     :
          // const EdgeInsets.symmetric(horizontal: 15,vertical: 5),

          child: ReadMoreText(
            widget.postDetail.text!,
            trimMode: TrimMode.Line,
            trimLines: widget.isFullView! ? 1000 : 3,
            colorClickableText: Colors.pink,
            style: widget.isFromSinglePost
                ? AppTextStyle.contentText1(textColor: AppColors.appBlack)
                : AppTextStyle.contentText0(textColor: AppColors.appBlack),
            lessStyle:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            moreStyle:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            trimLength: 5,
            trimCollapsedText: AppStrings.more,
            trimExpandedText: " ${AppStrings.less}",
            textAlign: TextAlign.start,
            annotations: [
              // Annotation(
              //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
              //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
              //     text: text,
              //     style: textStyle?.copyWith(color: Colors.blue),
              //   ),
              // ),
              //User name or handle
              Annotation(
                regExp: RegExp(r"\{\{mention:\{[^}]*\}\}\}"),
                spanBuilder: ({required String text, TextStyle? textStyle}) {
                  // Extract display text from encoded mention
                  String displayText = MentionParser.extractDisplayText(text);
                  String reference = MentionParser.extractReference(text);

                  return TextSpan(
                    text: displayText,
                    style: AppTextStyle.access0(textColor: AppColors.brandGreen),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // Use the display text for navigation
                        OnTapTag(context, displayText);
                      },
                  );
                },
              ),
              //URL
              Annotation(
                // regExp: RegExp(r'(https?://)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*/?',caseSensitive: true,multiLine: false),
                regExp: AppConstants.urlRegex,

                spanBuilder: ({required String text, TextStyle? textStyle}) =>
                    TextSpan(
                  text: text,
                  style: textStyle?.copyWith(color: AppColors.brandBlack),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      CommonMethods.opeAppWebView(
                          webUrl: text,
                          context:
                              AppConstants.globalNavigator.currentContext!);
                      //print(text); // Print the URL
                    },
                ),
              ),
            ],
          )

          // child: ReadMoreText(
          //   widget.postDetail.text!,
          //   trimLines: 5,
          //   trimMode: TrimMode.Line,
          //   trimCollapsedText: AppStrings.more,
          //   trimExpandedText: " ${AppStrings.less}",
          //   textAlign: TextAlign.start,
          //   style: widget.isFromSinglePost
          //       ? AppTextStyle.contentText1(textColor: AppColors.appBlack)
          //       : AppTextStyle.contentText0(textColor: AppColors.appBlack),
          //   lessStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          //   moreStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          // ),
          ),
    );
  }

  //endregion

  //region Counts
  Widget counts() {
    return Visibility(
      visible: widget.postDetail.likeCount != 0 ||
          widget.postDetail.commentCount != 0 ||
          (widget.postDetail.analyticsViewCount != null &&
              widget.postDetail.analyticsViewCount! > 0),
      child: Container(
        margin: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 0),
        child: Row(
          children: [
            Visibility(
              visible: widget.postDetail.likeCount != 0,
              child: InkWell(
                onTap: () {
                  postCardBloc.goToLikedUsedOrStoreScreen(
                      postReference: widget.postDetail.postOrCommentReference!);
                },
                child: Container(
                    margin: const EdgeInsets.only(right: 5),
                    child: Text(
                      "${widget.postDetail.likeCount} ${widget.postDetail.likeCount == 1 ? "like" : "likes"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0),
                    )),
              ),
            ),
            Visibility(
              visible: widget.postDetail.commentCount != 0,
              child: Container(
                  margin: const EdgeInsets.only(right: 5),
                  child: Text(
                      "${widget.postDetail.commentCount} ${widget.postDetail.commentCount == 1 ? "comment" : "comments"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0))),
            ),
            const Expanded(child: SizedBox()),
            Visibility(
              visible: widget.postDetail.analyticsViewCount != null &&
                  widget.postDetail.analyticsViewCount! > 0,
              child: Container(
                  margin: const EdgeInsets.only(right: 0),
                  child: Text(
                      "${widget.postDetail.analyticsViewCount} ${widget.postDetail.analyticsViewCount == 1 ? "view" : "views"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0))),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Action
  Widget action() {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          //Like
          VisibilityDetector(
            key: UniqueKey(),
            onVisibilityChanged: (visibilityInfo) {
              var visiblePercentage = visibilityInfo.visibleFraction * 100;
              // If visibility is 100% and is visited is false
              if (visiblePercentage == 100 &&
                  !postCardBloc.postDetail.isVisited) {
                //postCardBloc.visited();
              }
            },
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  widget.onTapHeart();
                },
                child: SvgPicture.asset(
                  fit: BoxFit.fill,
                  widget.postDetail.likeStatus!
                      ? AppImages.postLike
                      : AppImages.postDisLike,
                  color: widget.postDetail.likeStatus!
                      ? AppColors.red
                      : AppColors.appBlack,
                ),
              ),
            ),
          ),

          //Comment
          Visibility(
            visible: !widget.isFullView!,
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              height: 26,
              width: 26,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  widget.onTapPost();
                },
                child: SvgPicture.asset(AppImages.postComment,
                    color: AppColors.appBlack),
              ),
            ),
          ),
          //Repost
          InkWell(
            onTap: () {
              postCardBloc.rePost(postDetail: widget.postDetail);
            },
            child: SizedBox(
              height: 26,
              width: 26,
              child: SvgPicture.asset(
                AppImages.repost,
                color:
                    widget.postDetail.contentCategory == EntityType.REPOST.name
                        ? AppColors.brandGreen
                        : AppColors.appBlack,
              ),
            ),
          ),
          const Expanded(child: SizedBox()),

          //Share
          SizedBox(
            height: 26,
            width: 26,
            child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  widget.onTapShare();
                },
                child: SvgPicture.asset(
                  AppImages.sharePost,
                  color: AppColors.appBlack,
                )),
          ),
          const SizedBox(
            width: 10,
          ),
          //Save
          InkWell(
            onTap: () {
              postCardBloc.savePost(postDetail: widget.postDetail);
            },
            child: widget.postDetail.saveStatus!
                ? Container(
                    padding:
                        EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 4),
                    height: 26,
                    width: 26,
                    child: SvgPicture.asset(AppImages.savePostActive,
                        color: AppColors.appBlack))
                : SizedBox(
                    height: 26,
                    width: 26,
                    child: SvgPicture.asset(AppImages.savePost,
                        color: AppColors.appBlack)),
          ),
        ],
      ),
    );
  }
//endregion

//region Simple post heading
  Widget simplePostHeader() {
    return PostAndProductAppBar(
      verifiedWidget: VerifiedBadge(
        width: 15,
        height: 15,
        subscriptionType: widget.postDetail.createdBy!.subscriptionType,
      ),
      onTapProfileImage: () {
        widget.onTapProfileImage();
      },
      icon: widget.postDetail.createdBy!.icon,
      title: widget.postDetail.createdBy!.handle!,
      subTitle: postCardBloc.convertDateFormat(
          inputDateTimeString: widget.postDetail.createdDate!),
      entityType: widget.postDetail.createdBy!.entityType!,
      level: widget.postDetail.createdBy!.level.toString(),
      onTapOptions: () {
        postCardBloc.onTapDrawer(postDetail: widget.postDetail);
      },
    );
  }
//endregion

//region Reposted post heading
  Widget repostedPostHeader() {
    // Check if a subtitle (customTitle) is present
    bool hasSubtitle =
        widget.customTitle != null && widget.customTitle!.isNotEmpty;

    return PostAndProductAppBar(
      onTapProfileImage: () {
        widget.onTapProfileImage();
      },
      icon: widget.postDetail.createdBy!.icon,
      title: "",
      level: widget.postDetail.createdBy!.level.toString(),
      customTitle: hasSubtitle
          // If subtitle is present, show date beside handle
          ? Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.postDetail.createdBy!.handle!,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType:
                      widget.postDetail.createdBy!.subscriptionType,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  postCardBloc.convertDateFormat(
                      inputDateTimeString: widget.postDetail.createdDate!),
                  overflow: TextOverflow.ellipsis,
                  style:
                      AppTextStyle.smallText(textColor: AppColors.writingBlack1)
                          .copyWith(height: 0),
                )
              ],
            )
          // If no subtitle, only show handle (date will be shown below)
          : Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.postDetail.createdBy!.handle!,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType:
                      widget.postDetail.createdBy!.subscriptionType,
                ),
              ],
            ),
      // If no subtitle, show date as subtitle
      subTitle: !hasSubtitle
          ? postCardBloc.convertDateFormat(
              inputDateTimeString: widget.postDetail.createdDate!)
          : "",
      customSubTitle: hasSubtitle
          ? ReadMoreText(
              '${widget.customTitle}',
              trimMode: TrimMode.Line,
              trimLines: widget.isFullView ?? false ? 1000 : 3000,
              colorClickableText: Colors.pink,
              style: widget.isFromSinglePost
                  ? AppTextStyle.subTitle(textColor: AppColors.appBlack)
                      .copyWith(fontSize: 12)
                      .copyWith(height: 0)
                  : AppTextStyle.subTitle(textColor: AppColors.appBlack)
                      .copyWith(fontSize: 12)
                      .copyWith(height: 0),
              lessStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              moreStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              trimLength: 100000,
              trimCollapsedText: "",
              trimExpandedText: "",
              textAlign: TextAlign.start,
              annotations: [
                Annotation(
                  regExp: RegExp(r"\{\{mention:\{[^}]*\}\}\}"),
                  spanBuilder: ({required String text, TextStyle? textStyle}) {
                    // Extract display text from encoded mention
                    String displayText = MentionParser.extractDisplayText(text);

                    return TextSpan(
                      text: displayText,
                      style: AppTextStyle.access0(textColor: AppColors.brandGreen)
                          .copyWith(
                              fontSize: 12,
                              height: 0),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          // Use the display text for navigation
                          OnTapTag(context, displayText);
                        },
                    );
                  },
                ),
              ],
            )
          : null,
      entityType: widget.postDetail.createdBy!.entityType!,
      onTapOptions: () {
        postCardBloc.onTapDrawer(postDetail: widget.postDetail);
      },
    );
  }
//endregion

//region Tagged Items Dropdown
  Widget taggedItemsDropdown() {
    return Visibility(
      visible: widget.postDetail.taggedObjectsCount != null &&
               widget.postDetail.taggedObjectsCount! > 0,
      child: Container(
        margin: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 5),
        child: InkWell(
          onTap: () {
            postCardBloc.showTaggedItemsBottomSheet(
              postReference: widget.postDetail.postOrCommentReference!,
            );
          },
          child: Row(
            children: [
              Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: AppColors.writingBlack0,
              ),
              const SizedBox(width: 4),
              Text(
                'View tagged items',
                style: AppTextStyle.smallText(
                  textColor: AppColors.writingBlack0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
//endregion
}
