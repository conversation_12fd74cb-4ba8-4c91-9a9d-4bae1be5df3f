import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:base32/base32.dart';

class UploadFileService {
  // region Common Variables
  var dio = Dio();
  int uploadTimeOutInSecond = 1000;

  // endregion

  // region | Constructor |
  UploadFileService();

  // endregion

  //region Form header
  dynamic formHeader() {
    var headers = {
      'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
      'Content-Type': 'multipart/form-data',
    };
    //If web
    if (kIsWeb) {
      headers["Cache-Control-Speed"] = "medium";
    }
    return headers;
  }

  //endregion

  //region Decrypt response
  dynamic decryptResponse({required var response}) {
    //If web
    if (kIsWeb) {
      // Extract the value of the "buds" key
      String budsValue = response.data["buds"];
      // Decode the "buds" value using base32
      List<int> decodedBytes = base32.decode(budsValue);
      // Convert the decoded bytes to a JSON object
      String decodedString = utf8.decode(decodedBytes);
      Map<String, dynamic> decodedJson = json.decode(decodedString);
      return decodedJson;
    }
    //If mobile
    else {
      return response.data;
    }
  }

  //endregion

  // region upload product Images
  Future<void> uploadProductImage({
    required String url,
    required String fileNameWithExtension,
    required String filePath,
    var parameter,
  }) async {
    try {
      //print(url);
      // get file
      var file = await MultipartFile.fromFile(filePath,
          filename:
              CommonMethods.trimFileName(fileName: fileNameWithExtension));

      // generate body
      var body = {
        "product_image": file,
        "created_by": AppConstants.appData.storeId,
        "modified_by": AppConstants.appData.storeId
      };
      // body.addAll(parameter);

      // Headers
      var headers = formHeader();
      // create form data
      FormData formData = FormData.fromMap(body);

      // start upload file
      var responce = await dio
          .post(url,
              options: Options(
                headers: headers,
              ),
              data: formData,
              onSendProgress: (send, total) {})
          .timeout(Duration(seconds: uploadTimeOutInSecond));
      //print("Uploaded");
    } catch (e) {
      //print("Error occurred: $e");
    }
  }

// endregion

  // region upload store logo
  Future<void> uploadStoreLogo({
    required String url,
    required String fileNameWithExtension,
    required String filePath,
    var parameter,
  }) async {
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // generate body
    var body = {"icon": file};
    // body.addAll(parameter);

    // create form data
    FormData formData = FormData.fromMap(body);
    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .post(url,
            options: Options(
              headers: headers,
            ),
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region upload store cover image
  Future<void> uploadStoreCoverImage({
    required String url,
    required String fileNameWithExtension,
    required String filePath,
    var parameter,
  }) async {
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // generate body
    var body = {"cover_image": file};
    // body.addAll(parameter);

    // create form data
    FormData formData = FormData.fromMap(body);
    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .post(url,
            options: Options(
              headers: headers,
            ),
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Add Seller Trust Center Add Document
  Future<void> sellerTrustCenterUploadDocument({
    required String fileNameWithExtension,
    required String filePath,
    required String storeRef,
    required String documentName,
    required bool showPublic,
  }) async {
    //print("File path $filePath");
    //print("fileNameWithExtension $fileNameWithExtension");
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    //print(file);
    // generate body
    var body = {
      "store_reference": storeRef,
      "document_file": file,
      "documentname": documentName,
      "show_to_public": showPublic
    };
    // body.addAll(parameter);

    //print(body);
    // create form data
    FormData formData = FormData.fromMap(body);

    // Headers
    var headers = {
      'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
      'Content-Type': 'multipart/form-data',
    };

    // start upload file
    var responce = await dio
        .post(
            options: Options(
              headers: headers,
            ),
            AppConstants.addTrustCenterDocument,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Update Seller Trust Center Document and Name
  Future<void> sellerTrustCenterUpdateDocumentAndName({
    required String fileNameWithExtension,
    required String filePath,
    required String storeRef,
    required String documentName,
    required bool showPublic,
    required int documentId,
  }) async {
    //print("File path $filePath");
    //print("fileNameWithExtension $fileNameWithExtension");
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));
    //print(file);

    // generate body
    var body = {
      "store_reference": storeRef,
      "document_file": file,
      "documentname": documentName,
      "show_to_public": showPublic
    };
    // body.addAll(parameter);

    //print(body);
    // create form data
    FormData formData = FormData.fromMap(body);

    var url = "${AppConstants.editTrustCenterDocumentAndName}$documentId/";
    //print(url);

    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .put(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Upload Create User profile
  Future<void> uploadUserProfileImage({
    required String url,
    required String userName,
    required String location,
    required String gender,
  }) async {
    // get file
    //var file = await MultipartFile.fromFile(filePath, filename: fileNameWithExtension);
    //print(url);
    // generate body
    var body = {
      "user_name": userName,
      "user_location": location,
      "gender": gender
    };
    // body.addAll(parameter);

    // create form data
    FormData formData = FormData.fromMap(body);

    // Headers
    var headers = formHeader();
    // start upload file
    var responce = await dio
        .put(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
  }

// endregion

  // region Add comment image
  Future<void> addCommentImage({
    required String url,
    required String fileNameWithExtension,
    required String filePath,
    required int commentId,
  }) async {
    //print(url);
    //print("File path $filePath");
    //print("fileNameWithExtension $fileNameWithExtension");
    //print(url);

    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // Headers
    var headers = formHeader();
    //print(file);
    // generate body
    var body = {
      "commentid": commentId,
      "image": file,
      "created_by": AppConstants.appData.userId
    };
    // body.addAll(parameter);

    //print(body);
    // create form data
    FormData formData = FormData.fromMap(body);

    // start upload file
    var responce = await dio
        .post(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Add Support documents
  Future<void> addSupportDocuments({
    required String fileNameWithExtension,
    required String filePath,
    required int feedbackId,
    required bool isOnlyImage,
  }) async {
    //print("File path $filePath");
    //print("fileNameWithExtension $fileNameWithExtension");
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    //print(file);
    // generate body
    var body = {
      "images": isOnlyImage ? file : null,
      "attachments": !isOnlyImage ? file : null
    };
    // body.addAll(parameter);

    ///Only images

    //print(body);
    // create form data
    FormData formData = FormData.fromMap(body);

    //Url
    var url = "${AppConstants.feedbackImage}$feedbackId/";

    //print(url);

    // Headers
    var headers = formHeader();
    // start upload file
    var responce = await dio
        .post(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Add User profile pic
  Future<void> addUserProfilePic({
    required String fileNameWithExtension,
    required String filePath,
  }) async {
    //print("File path $filePath");
    //print("fileNameWithExtension $fileNameWithExtension");
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    //print(file);
    // generate body
    var body = {
      "icon": file,
    };
    // body.addAll(parameter);

    ///Only images

    //print(body);
    // create form data
    FormData formData = FormData.fromMap(body);

    //Url
    var url =
        "${AppConstants.updateUserProfilePic}${AppConstants.appData.userReference}/";

    //print(url);
    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .patch(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  ///Post
  // region Create post
  Future<String> createPost({
    required List<String> filePaths,
    required String postText,
    String? userReference,
    String? storeReference,
    List<Map<String, dynamic>>? taggedReferences,
    int uploadTimeOutInSeconds = 300,
  }) async {
    try {
      var formData = FormData();

      // Add files to form data
      for (String path in filePaths) {
        formData.files.add(
          MapEntry(
            'post_images',
            await MultipartFile.fromFile(
              path,
              filename: path.split('/').last, // Extract filename from path
            ),
          ),
        );
      }

      // Add text fields to form data
      formData.fields.addAll([
        MapEntry('post_text', postText),
        MapEntry('user_reference', userReference ?? ""),
        MapEntry('store_reference', storeReference ?? ""),
      ]);

      // Add tagged references if available
      if (taggedReferences != null && taggedReferences.isNotEmpty) {
        formData.fields.add(MapEntry('tagged_references_json', jsonEncode(taggedReferences)));
      }

      var url = AppConstants.createPost;

      // Headers
      var headers = formHeader();

      // Send post request
      var response = await dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: (send, total) {
          // Handle progress if needed
        },
      ).timeout(Duration(seconds: uploadTimeOutInSeconds));

      //print(response.data);

      // Decrypt the response using the decryptResponse method
      var decryptedResponse = decryptResponse(response: response);

      // Return the value of the "post_reference" key
      return decryptedResponse["post"]["post_reference"];
    } catch (e) {
      //print('Error occurred: $e');
      throw AppStrings.commonErrorMessage;
    }
  }

// endregion

  // region Edit post
  Future<String> editPost({
    List<String>? filePaths,
    required String postText,
    String? postReference,
    List<Map<String, dynamic>>? taggedReferences,
    int uploadTimeOutInSecond = 300,
  }) async {
    try {
      var formData = FormData();

      if (filePaths != null && filePaths.isNotEmpty) {
        for (String path in filePaths) {
          formData.files.addAll([
            MapEntry(
                'post_images',
                await MultipartFile.fromFile(
                  path,
                  filename: path.split('/').last, // Extract filename from path
                )),
          ]);
        }
      }

      formData.fields.addAll([
        MapEntry('post_text', postText),
        MapEntry('post_reference', postReference ?? ""),
      ]);

      // Add tagged references if available
      if (taggedReferences != null && taggedReferences.isNotEmpty) {
        formData.fields.add(MapEntry('tagged_references_json', jsonEncode(taggedReferences)));
      }

      var url = AppConstants.editPost;

      // Headers
      var headers = formHeader();

      var response = await dio.put(
        options: Options(
          headers: headers,
        ),
        url,
        data: formData,
        onSendProgress: (send, total) {
          // Handle progress if needed
        },
      ).timeout(Duration(seconds: uploadTimeOutInSecond));

      //print(response.data);
      // Decrypt the response using the decryptResponse method
      var decryptedResponse = decryptResponse(response: response);

      // Return the value of the "reference" key
      return decryptedResponse["post"]["post_reference"];
      return response.data["post"]
          ["post_reference"]; // Return the PostDetail object
    } catch (e) {
      //print('Error occurred: $e');
      throw e; // Rethrow the error to handle it at the calling site
    }
  }

  // endregion

  // region Delete post
  Future<void> deletePost({
    required String postReference,
    int uploadTimeOutInSecond = 300,
  }) async {
    try {
      var formData = FormData();

      // if (filePaths.isNotEmpty) {
      //   List<MultipartFile> files = [];
      //
      //   for (String path in filePaths) {
      //     var file = await MultipartFile.fromFile(
      //       path,
      //       filename: CommonMethods.getEpocTime(),
      //     );
      //     files.add(file);
      //   }
      //
      //   formData.files.addAll(
      //     files.map((file) => MapEntry('post_images', file)),
      //   );
      // }

      formData.fields.addAll([
        MapEntry('post_reference', postReference),
      ]);

      var url = AppConstants.createPost;

      // Headers
      var headers = formHeader();

      var response = await dio
          .delete(
            options: Options(
              headers: headers,
            ),
            url,
            data: formData,
          )
          .timeout(Duration(seconds: uploadTimeOutInSecond));

      //print(response.data);
    } catch (e) {
      //print('Error occurred: $e');
      // Handle errors or exceptions here
    }
  }

// endregion

  ///Comment
  // region Create comment
  Future<String> createComment({
    required List<String> filePaths,
    required String commentText,
    required CommentEnums commentEnums,
    required String parentReference,
    String? reviewCount,
    int uploadTimeOutInSecond = 300,
  }) async {
    try {
      // Prepare form data
      var formData = FormData();

      if (filePaths.isNotEmpty) {
        for (String path in filePaths) {
          formData.files.add(
            MapEntry(
              'comment_images',
              await MultipartFile.fromFile(
                path,
                filename: path.split('/').last, // Extract filename from path
              ),
            ),
          );
        }
      }

      // Add required fields to the form
      formData.fields.addAll([
        MapEntry('parent_reference', parentReference),
        MapEntry('comment_text', commentText),
        MapEntry('comment_type', commentEnums.name ?? ""),
        MapEntry(
          'entity_reference',
          "${AppConstants.appData.isUserView! ? AppConstants.appData.userReference : AppConstants.appData.storeReference}",
        ),
      ]);

      // Add 'rating_count' only if reviewCount is not null
      if (reviewCount != null) {
        formData.fields.add(MapEntry('rating_count', reviewCount));
      }

      var url = AppConstants.createComment;
      var headers = formHeader();

      var response = await dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: (send, total) {
          // Handle progress if needed
        },
      ).timeout(Duration(seconds: uploadTimeOutInSecond));

      // Decrypt the response
      var decryptedResponse = decryptResponse(response: response);

      // Return the value of the "reference" key
      return decryptedResponse["reference"];
    } catch (e) {
      if (e is DioError) {
        // Handle status code 400
        if (e.response?.statusCode == 400) {
          if (e.response?.data["is_custom"] != null &&
              e.response?.data["is_custom"] == true) {
            throw ApiErrorResponseMessage(
                message: e.response?.data["message"].first);
            // CommonMethods.toastMessage(,AppConstants.userStoreCommonBottomNavigationContext);
            // throw e.response?.data["message"];
          }
        }
      }

      // Handle other types of exceptions
      //print('Unexpected error occurred: $e');
      throw Exception(AppStrings.commonErrorMessage);
    }
  }

// endregion

  // region Edit comment
  Future<String> editComment({
    List<String>? filePaths,
    required String postText,
    String? postReference,
    String? reviewCount,
    List<Map<String, dynamic>>? taggedReferences,
    int uploadTimeOutInSecond = 300,
  }) async {
    try {
      var formData = FormData();

      if (filePaths != null && filePaths.isNotEmpty) {
        for (String path in filePaths) {
          formData.files.addAll([
            MapEntry(
                'comment_images',
                await MultipartFile.fromFile(
                  path,
                  filename: path.split('/').last, // Extract filename from path
                )),
          ]);
        }
      }

      formData.fields.addAll([
        MapEntry('comment_text', postText),
        MapEntry('comment_reference', postReference ?? ""),
      ]);

      // Add 'rating_count' only if reviewCount is not null
      if (reviewCount != null) {
        formData.fields.add(MapEntry('rating_count', reviewCount));
      }

      // Add tagged references if available
      if (taggedReferences != null && taggedReferences.isNotEmpty) {
        formData.fields.add(MapEntry('tagged_references_json', jsonEncode(taggedReferences)));
      }

      var url = AppConstants.editComment;

      // Headers
      var headers = formHeader();

      var response = await dio.post(
        options: Options(
          headers: headers,
        ),
        url,
        data: formData,
        onSendProgress: (send, total) {
          // Handle progress if needed
        },
      ).timeout(Duration(seconds: uploadTimeOutInSecond));

      //print(response.data);
      // Decrypt the response using the decryptResponse method
      var decryptedResponse = decryptResponse(response: response);

      // Return the value of the "reference" key
      return decryptedResponse["reference"];
      return response.data["reference"]; // Return the PostDetail object
    } catch (e) {
      //print('Error occurred: $e');
      throw e; // Rethrow the error to handle it at the calling site
    }
  }

  // endregion

  ///Store signature

  // region Add store signature
  Future<void> addStoreSignature({
    required String fileNameWithExtension,
    required String filePath,
    var parameter,
  }) async {
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // generate body
    var body = {"signature": file};
    // body.addAll(parameter);

    // create form data
    FormData formData = FormData.fromMap(body);
    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .post(
            "${AppConstants.addAndEditStoreSignature}${AppConstants.appData.storeReference}/",
            options: Options(
              headers: headers,
            ),
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  // region Edit store signature
  Future<void> editStoreSignature({
    required String fileNameWithExtension,
    required String filePath,
    var parameter,
  }) async {
    // get file
    var file = await MultipartFile.fromFile(filePath,
        filename: CommonMethods.trimFileName(fileName: fileNameWithExtension));

    // generate body
    var body = {"signature": file};
    // body.addAll(parameter);

    // create form data
    FormData formData = FormData.fromMap(body);
    // Headers
    var headers = formHeader();

    // start upload file
    var responce = await dio
        .patch(
            "${AppConstants.addAndEditStoreSignature}${AppConstants.appData.storeReference}/",
            options: Options(
              headers: headers,
            ),
            data: formData,
            onSendProgress: (send, total) {})
        .timeout(Duration(seconds: uploadTimeOutInSecond));
    //print(responce.data);
  }

// endregion

  ///Story
  // region Upload story image

  Future<String> uploadStoryImage({
    required List<Map<String, dynamic>> imageDataList,
    required String storyReference,
    int uploadTimeOutInSeconds = 300,
  }) async {
    try {
      // Initialize FormData
      var formData = FormData();

      // Add story_reference as a form field
      formData.fields.add(MapEntry('story_reference', storyReference));

      // Create a comma-separated string for all image hashes
      String imageHashes =
          imageDataList.map((imageData) => imageData['hash']).join(',');

      // Add image_hash as a form field
      formData.fields.add(MapEntry('image_hash', imageHashes));

      // Add each image file to the form data
      for (var imageData in imageDataList) {
        if (imageData.containsKey('path')) {
          formData.files.add(
            MapEntry(
              'image_file',
              await MultipartFile.fromFile(
                imageData['path'],
                filename: imageData['path'].split('/').last, // Extract filename
              ),
            ),
          );
        }
      }

      // Set URL for the upload
      var url =
          'https://e2e-77-175.ssdcloudindia.net/dev/content/story_image_upload/';

      // Set headers, including Authorization
      var headers = formHeader();

      // Send POST request
      var response = await Dio().post(
        url,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: (sent, total) {
          // Optional: track upload progress here
        },
      ).timeout(Duration(seconds: uploadTimeOutInSeconds));

      // Print or handle response
      //print(response.data);

      // Handle or parse the response as needed
      if (response.statusCode == 200) {
        // Assume 'post_reference' is in the response body if successful
        return response.data["post_reference"];
      } else {
        throw Exception('Failed to upload story image');
      }
    } catch (e) {
      //print('Error occurred: $e');
      throw 'An error occurred while uploading the story image.';
    }
  }

// endregion
}
