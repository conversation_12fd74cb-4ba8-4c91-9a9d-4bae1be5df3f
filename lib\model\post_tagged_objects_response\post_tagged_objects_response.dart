class PostTaggedObjectsResponse {
  String? message;
  List<PostTaggedObjectsData>? data;

  PostTaggedObjectsResponse({
    this.message,
    this.data,
  });

  PostTaggedObjectsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <PostTaggedObjectsData>[];
      json['data'].forEach((v) {
        data!.add(PostTaggedObjectsData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PostTaggedObjectsData {
  String? postReference;
  List<TaggedObjectsDetails>? taggedObjectsDetails;

  PostTaggedObjectsData({
    this.postReference,
    this.taggedObjectsDetails,
  });

  PostTaggedObjectsData.fromJson(Map<String, dynamic> json) {
    postReference = json['post_reference'];
    if (json['tagged_objects_details'] != null) {
      taggedObjectsDetails = <TaggedObjectsDetails>[];
      json['tagged_objects_details'].forEach((v) {
        taggedObjectsDetails!.add(TaggedObjectsDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['post_reference'] = postReference;
    if (taggedObjectsDetails != null) {
      data['tagged_objects_details'] = taggedObjectsDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TaggedObjectsDetails {
  List<TaggedObject>? products;
  List<TaggedObject>? stores;
  List<TaggedObject>? users;

  TaggedObjectsDetails({
    this.products,
    this.stores,
    this.users,
  });

  TaggedObjectsDetails.fromJson(Map<String, dynamic> json) {
    if (json['products'] != null) {
      products = <TaggedObject>[];
      json['products'].forEach((v) {
        products!.add(TaggedObject.fromJson(v));
      });
    }
    if (json['stores'] != null) {
      stores = <TaggedObject>[];
      json['stores'].forEach((v) {
        stores!.add(TaggedObject.fromJson(v));
      });
    }
    if (json['users'] != null) {
      users = <TaggedObject>[];
      json['users'].forEach((v) {
        users!.add(TaggedObject.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (products != null) {
      data['products'] = products!.map((v) => v.toJson()).toList();
    }
    if (stores != null) {
      data['stores'] = stores!.map((v) => v.toJson()).toList();
    }
    if (users != null) {
      data['users'] = users!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TaggedObject {
  String? icon;
  String? name;
  String? type;
  int? order;
  String? handle;
  String? reference;

  TaggedObject({
    this.icon,
    this.name,
    this.type,
    this.order,
    this.handle,
    this.reference,
  });

  TaggedObject.fromJson(Map<String, dynamic> json) {
    icon = json['icon'];
    name = json['name'];
    type = json['type'];
    order = json['order'];
    handle = json['handle'];
    reference = json['reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['icon'] = icon;
    data['name'] = name;
    data['type'] = type;
    data['order'] = order;
    data['handle'] = handle;
    data['reference'] = reference;
    return data;
  }

  // Convert TaggedObject to SuggestionItem for reusing existing UI components
  Map<String, dynamic> toSuggestionItem() {
    return {
      'type': type,
      'reference': reference,
      'primary_text': handle,
      'secondary_text': name,
      'image_url': icon,
      'store_handle': type == 'PRODUCT' ? handle : null,
      'store_name': type == 'PRODUCT' ? name : null,
    };
  }
}
