class PostTaggedObjectsResponse {
  String? message;
  List<PostTaggedObjectsData>? data;

  PostTaggedObjectsResponse({
    this.message,
    this.data,
  });

  PostTaggedObjectsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      data = <PostTaggedObjectsData>[];
      json['data'].forEach((v) {
        data!.add(PostTaggedObjectsData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PostTaggedObjectsData {
  String? postReference;
  List<TaggedObjectsDetails>? taggedObjectsDetails;

  PostTaggedObjectsData({
    this.postReference,
    this.taggedObjectsDetails,
  });

  PostTaggedObjectsData.fromJson(Map<String, dynamic> json) {
    postReference = json['post_reference'];
    if (json['tagged_objects_details'] != null) {
      taggedObjectsDetails = <TaggedObjectsDetails>[];
      json['tagged_objects_details'].forEach((v) {
        taggedObjectsDetails!.add(TaggedObjectsDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['post_reference'] = postReference;
    if (taggedObjectsDetails != null) {
      data['tagged_objects_details'] = taggedObjectsDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // Helper method to get all tagged objects flattened into lists
  Map<String, List<TaggedObject>> getAllTaggedObjects() {
    Map<String, List<TaggedObject>> result = {
      'products': <TaggedObject>[],
      'stores': <TaggedObject>[],
      'users': <TaggedObject>[],
    };

    if (taggedObjectsDetails != null) {
      for (var details in taggedObjectsDetails!) {
        if (details.products != null) {
          result['products']!.addAll(details.products!);
        }
        if (details.stores != null) {
          result['stores']!.addAll(details.stores!);
        }
        if (details.users != null) {
          result['users']!.addAll(details.users!);
        }
      }
    }

    return result;
  }
}

class TaggedObjectsDetails {
  List<TaggedObject>? products;
  List<TaggedObject>? stores;
  List<TaggedObject>? users;

  TaggedObjectsDetails({
    this.products,
    this.stores,
    this.users,
  });

  TaggedObjectsDetails.fromJson(Map<String, dynamic> json) {
    if (json['products'] != null) {
      products = <TaggedObject>[];
      json['products'].forEach((v) {
        products!.add(TaggedObject.fromJson(v));
      });
    }
    if (json['stores'] != null) {
      stores = <TaggedObject>[];
      json['stores'].forEach((v) {
        stores!.add(TaggedObject.fromJson(v));
      });
    }
    if (json['users'] != null) {
      users = <TaggedObject>[];
      json['users'].forEach((v) {
        users!.add(TaggedObject.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (products != null) {
      data['products'] = products!.map((v) => v.toJson()).toList();
    }
    if (stores != null) {
      data['stores'] = stores!.map((v) => v.toJson()).toList();
    }
    if (users != null) {
      data['users'] = users!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TaggedObject {
  String? icon;
  String? name;
  String? type;
  int? order;
  String? handle;
  String? reference;

  TaggedObject({
    this.icon,
    this.name,
    this.type,
    this.order,
    this.handle,
    this.reference,
  });

  TaggedObject.fromJson(Map<String, dynamic> json) {
    icon = json['icon'];
    name = json['name'];
    type = json['type'];
    order = json['order'];
    handle = json['handle'];
    reference = json['reference'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['icon'] = icon;
    data['name'] = name;
    data['type'] = type;
    data['order'] = order;
    data['handle'] = handle;
    data['reference'] = reference;
    return data;
  }

  // Convert TaggedObject to SuggestionItem for reusing existing UI components
  Map<String, dynamic> toSuggestionItem() {
    return {
      'type': type,
      'reference': reference,
      'primary_text': handle,
      'secondary_text': name,
      'image_url': icon,
      'store_handle': type == 'PRODUCT' ? handle : null,
      'store_name': type == 'PRODUCT' ? name : null,
    };
  }

  // Test method to verify parsing with your API response
  static void testApiResponseParsing() {
    // Your actual API response
    Map<String, dynamic> testResponse = {
      "message": "success",
      "data": [
        {
          "post_reference": "PO202506161901057441",
          "tagged_objects_details": [
            {
              "products": []
            },
            {
              "stores": []
            },
            {
              "users": [
                {
                  "icon": "profile_image/er_1735977661551.jpg",
                  "name": "Manoj",
                  "type": "USER",
                  "order": 1,
                  "handle": "manoj_subramanyam",
                  "reference": "U1729490957974"
                }
              ]
            }
          ]
        }
      ]
    };

    try {
      PostTaggedObjectsResponse response = PostTaggedObjectsResponse.fromJson(testResponse);
      print("✅ Parsing successful!");
      print("Message: ${response.message}");
      print("Data count: ${response.data?.length}");

      if (response.data != null && response.data!.isNotEmpty) {
        var allTaggedObjects = response.data!.first.getAllTaggedObjects();
        print("Products: ${allTaggedObjects['products']?.length}");
        print("Stores: ${allTaggedObjects['stores']?.length}");
        print("Users: ${allTaggedObjects['users']?.length}");

        if (allTaggedObjects['users']!.isNotEmpty) {
          var user = allTaggedObjects['users']!.first;
          print("First user: ${user.name} (@${user.handle})");
        }
      }
    } catch (e) {
      print("❌ Parsing failed: $e");
    }
  }
}
