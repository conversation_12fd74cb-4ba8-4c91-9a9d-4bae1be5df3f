import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields.dart';
import 'package:swadesic/features/seller/add_product/add_product_common_widgets.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'add_product_bloc.dart';

// region Add Product Screen
class AddProductScreen extends StatefulWidget {
  final int storeId;
  final String storeReference;
  const AddProductScreen(
      {Key? key, required this.storeId, required this.storeReference})
      : super(key: key);

  @override
  _AddProductScreenState createState() => _AddProductScreenState();
}
// endregion

class _AddProductScreenState extends State<AddProductScreen> {
  // region Bloc
  late AddProductBloc addProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    addProductBloc =
        AddProductBloc(context, widget.storeId, widget.storeReference);
    addProductBloc.init();
    super.initState();
  }
  // endregion

  //region Dispose
  @override
  void dispose() {
    addProductBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Clear images for both platforms
        AppConstants.multipleSelectedImage.clear();
        AppConstants.webProductImages.clear();
        return true;
      },
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
          // resizeToAvoidBottomInset: false,

          // resizeToAvoidBottomPadding: false,
          appBar: appBar(),
          backgroundColor: AppColors.appWhite,

          body: SafeArea(child: body()),
        ),
      ),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.addProduct,
        isDefaultMenuVisible: false,
        onTapLeading: () {
          addProductBloc.onTapBack();
        },
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: true,
        textButtonWidget:
            AppCommonWidgets.appBarTextButtonText(text: AppStrings.next),
        onTapTextButton: () {
          addProductBloc.goToAddProductPreview();
        });
  }

  //endregion

  // region Body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: ListView(
        shrinkWrap: true,
        // mainAxisAlignment: MainAxisAlignment.start,
        // crossAxisAlignment: CrossAxisAlignment.start,
        // mainAxisSize: MainAxisSize.min,
        children: [
          //brandName(),
          verticalSizedBox(38),
          addImage(),
          verticalSizedBox(34),
          addEditProductTextFields(),
          // brandName(),
          // verticalSizedBox(24),
          //  productName(),
          //  verticalSizedBox(24),
          // productCategory(),
          // verticalSizedBox(24),
          // productDescription(),
          //  verticalSizedBox(24),
          //  productPromotion(),
          // verticalSizedBox(24),
          // productStock(),
          //  verticalSizedBox(24),
          // hashTags(),
          // verticalSizedBox(24),
          // productPricing(),
          // verticalSizedBox(15),
          // whitelabelCharge(),
          // verticalSizedBox(5),
          // sellingAt(),
          // verticalSizedBox(24),

          options(),

          AppCommonWidgets.bottomListSpace(context: context)
        ],
      ),
    );
  }

  // endregion

  //region Add Images

  Widget addImage() {
    return Align(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StreamBuilder<bool>(
              stream: addProductBloc.imageCtrl.stream,
              builder: (context, snapshot) {
                // For web platform
                if (kIsWeb) {
                  if (AppConstants.webProductImages.isNotEmpty) {
                    return InkWell(
                      onTap: () {
                        addProductBloc.goToSelectedImageScreen();
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(CommonMethods()
                            .getBorderRadius(
                                height: 75,
                                imageType: CustomImageContainerType.product)),
                        child: Container(
                            height: 75,
                            width: 75,
                            decoration: const BoxDecoration(
                                color: AppColors.lightGray2,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(11))),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  CommonMethods().getBorderRadius(
                                      height: 75,
                                      imageType:
                                          CustomImageContainerType.product)),
                              // Display the first web image
                              child: Image.memory(
                                AppConstants.webProductImages[0]['bytes'],
                                fit: BoxFit.fill,
                                height: 200,
                                width: 200,
                              ),
                            )),
                      ),
                    );
                  }
                }
                // For mobile platform
                else if (AppConstants.multipleSelectedImage.isNotEmpty) {
                  return InkWell(
                    onTap: () {
                      addProductBloc.goToSelectedImageScreen();
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(CommonMethods()
                          .getBorderRadius(
                              height: 75,
                              imageType: CustomImageContainerType.product)),
                      child: Container(
                          height: 75,
                          width: 75,
                          decoration: const BoxDecoration(
                              color: AppColors.lightGray2,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(11))),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(CommonMethods()
                                .getBorderRadius(
                                    height: 75,
                                    imageType:
                                        CustomImageContainerType.product)),
                            child: Image.file(
                              File(AppConstants.multipleSelectedImage[0].path),
                              fit: BoxFit.fill,
                              cacheHeight: 200,
                              cacheWidth: 200,
                            ),
                          )),
                    ),
                  );
                }

                // Default placeholder when no images are selected
                return ClipRRect(
                  borderRadius: BorderRadius.circular(CommonMethods()
                      .getBorderRadius(
                          height: 75,
                          imageType: CustomImageContainerType.product)),
                  child: Container(
                    height: 75,
                    width: 75,
                    decoration: const BoxDecoration(
                        color: AppColors.lightGray2,
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: SvgPicture.asset(AppImages.productPlaceHolder),
                  ),
                );
              }),
          verticalSizedBox(24),
          Consumer<AppConfigDataModel>(
            builder: (BuildContext context, AppConfigDataModel value,
                Widget? child) {
              return CupertinoButton(
                  alignment: Alignment.center,
                  borderRadius: const BorderRadius.all(Radius.circular(9)),
                  color: AppColors.brandBlack,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppImages.plus,
                          color: AppColors.appWhite, height: 16, width: 16),
                      horizontalSizedBox(10),
                      Text(
                        "${AppStrings.addImages} (up to ${value.appConfig!.productImageLimit})",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      )
                    ],
                  ),
                  onPressed: () {
                    addProductBloc.goToAddImage();
                  });
            },
          )
        ],
      ),
    );
  }

  //endregion

// Add edit product Text fields
  Widget addEditProductTextFields() {
    return const AddEditProductFields();
  }
  //endregion

  //region Options
  Widget options() {
    return StreamBuilder<bool>(
        stream: addProductBloc.optionsRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              addLabels(),
              verticalSizedBox(24),
              deliverySetting(),
              verticalSizedBox(24),
              setReturnSettings(),
              verticalSizedBox(24),
              // addAStory(),
            ],
          );
        });
  }
  //endregion

  //region Add labels
  Widget addLabels() {
    return AddProductCommonWidgets.deliveryReturnButton(
      buttonName: AppStrings.addSwadesicLabels,
      onPress: () {
        addProductBloc.goToLabels(storeReference: widget.storeReference);
      },
      context: context,
      isDoneVisible: addProductBloc.product.swadeshiBrand != null &&
          addProductBloc.product.swadeshiMade != null,
    );
  }
  //endregion

  //region Delivery Setting
  Widget deliverySetting() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return AddProductCommonWidgets.deliveryReturnButton(
            context: context,
            buttonName: AppStrings.setDeliverySettings,
            onPress: () {
              addProductBloc.goToDeliverSettingScreen();
            },
            //1. Store level setting is true
            //2. Delivery setting is in first time added setting
            isDoneVisible: data.storeDashBoard.deliverySettings! ||
                addProductBloc.addProductLevelDeliverySettingResponse
                        .deliverySettingData !=
                    null);
      },
    );
  }
  //endregion

  //region Set Return settings
  Widget setReturnSettings() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return AddProductCommonWidgets.deliveryReturnButton(
            buttonName: AppStrings.setReturnSettings,
            onPress: () {
              addProductBloc.goToReturnAndWarranty();
            },
            context: context,
            isDoneVisible: data.storeDashBoard.warrantyAndReturn! ||
                addProductBloc.addProductLevelReturnSettingresponse.data !=
                    null);
      },
    );
  }
  //endregion

  //region Add a story
  // Widget addAStory(){
  //   return AddProductCommonWidgets.deliveryReturnButton(buttonName: AppStrings.tagStories, onPress: (){
  //     addProductBloc.goToTagStories();
  //   }, context: context,
  //       isDoneVisible: false
  //
  //   );
  // }
//endregion
}
