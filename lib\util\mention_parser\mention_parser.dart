import 'dart:convert';
import 'package:flutter/foundation.dart';

class MentionParser {
  /// Parses encoded mention and extracts display text
  /// Input: {{mention:{"reference":"S1721930951916","display_text":"@kitten_kreeps"}}}
  /// Output: @kitten_kreeps
  static String extractDisplayText(String encodedMention) {
    try {
      // Debug logging
      if (kDebugMode) {
        print('MentionParser.extractDisplayText - Input: $encodedMention');
      }

      // Handle nested mentions by finding the correct JSON part
      String jsonPart = _extractJsonFromMention(encodedMention);

      if (kDebugMode) {
        print('MentionParser.extractDisplayText - JSON part: $jsonPart');
      }

      // Parse the JSON
      Map<String, dynamic> mentionData = jsonDecode(jsonPart);

      // Get the display text
      String displayText = mentionData['display_text'] ?? encodedMention;

      // Recursively parse any nested mentions in the display text
      if (hasEncodedMentions(displayText)) {
        displayText = convertToDisplayText(displayText);
      }

      if (kDebugMode) {
        print('MentionParser.extractDisplayText - Output: $displayText');
      }

      return displayText;
    } catch (e) {
      // If parsing fails, return the original text
      if (kDebugMode) {
        print('MentionParser.extractDisplayText - Error: $e');
      }
      return encodedMention;
    }
  }

  /// Extracts reference from encoded mention
  /// Input: {{mention:{"reference":"S1721930951916","display_text":"@kitten_kreeps"}}}
  /// Output: S1721930951916
  static String extractReference(String encodedMention) {
    try {
      // Handle nested mentions by finding the correct JSON part
      String jsonPart = _extractJsonFromMention(encodedMention);

      // Parse the JSON
      Map<String, dynamic> mentionData = jsonDecode(jsonPart);

      // Return the reference
      return mentionData['reference'] ?? '';
    } catch (e) {
      // If parsing fails, return empty string
      return '';
    }
  }

  /// Helper method to extract JSON part from mention, handling nested braces
  static String _extractJsonFromMention(String encodedMention) {
    // Find the start of the JSON part after {{mention:
    int startIndex = encodedMention.indexOf('{{mention:');
    if (startIndex == -1) return '';

    startIndex += '{{mention:'.length;

    // Find the matching closing braces by counting brace levels
    int braceCount = 0;
    int endIndex = startIndex;

    for (int i = startIndex; i < encodedMention.length; i++) {
      if (encodedMention[i] == '{') {
        braceCount++;
      } else if (encodedMention[i] == '}') {
        if (braceCount == 0) {
          // This is the first closing brace after the JSON content
          endIndex = i;
          break;
        } else {
          braceCount--;
        }
      }
    }

    return encodedMention.substring(startIndex, endIndex);
  }

  /// Converts encoded text to display text for UI
  /// Replaces all encoded mentions with their display text
  static String convertToDisplayText(String encodedText) {
    if (kDebugMode) {
      print('MentionParser.convertToDisplayText - Input: $encodedText');
    }

    // Get all encoded mentions using the manual parser
    List<String> mentions = getAllEncodedMentions(encodedText);

    String result = encodedText;

    // Replace each mention with its display text
    for (String mention in mentions) {
      String displayText = extractDisplayText(mention);

      if (kDebugMode) {
        print('MentionParser.convertToDisplayText - Replacing: $mention -> $displayText');
      }

      result = result.replaceFirst(mention, displayText);
    }

    if (kDebugMode) {
      print('MentionParser.convertToDisplayText - Output: $result');
    }

    return result;
  }

  /// Converts display text back to encoded format
  /// This is used when we need to send data to API
  static String convertToEncodedText(String displayText, List<Map<String, dynamic>> mentions) {
    String result = displayText;
    
    for (Map<String, dynamic> mention in mentions) {
      String displayMention = mention['display_text'] ?? '';
      String reference = mention['reference'] ?? '';
      
      if (displayMention.isNotEmpty && reference.isNotEmpty) {
        String encodedMention = '{{mention:{"reference":"$reference","display_text":"$displayMention"}}}';
        result = result.replaceAll(displayMention, encodedMention);
      }
    }
    
    return result;
  }

  /// Checks if a string contains encoded mentions
  static bool hasEncodedMentions(String text) {
    return text.contains('{{mention:');
  }

  /// Gets all encoded mentions from text
  static List<String> getAllEncodedMentions(String text) {
    List<String> mentions = [];
    int index = 0;

    while (index < text.length) {
      int startIndex = text.indexOf('{{mention:', index);
      if (startIndex == -1) break;

      // Find the matching closing braces
      int endIndex = _findMatchingClosingBraces(text, startIndex);
      if (endIndex != -1) {
        String mention = text.substring(startIndex, endIndex + 2); // +2 for }}
        mentions.add(mention);
        index = endIndex + 2;
      } else {
        break;
      }
    }

    return mentions;
  }

  /// Helper method to find matching closing braces for a mention
  static int _findMatchingClosingBraces(String text, int startIndex) {
    int braceCount = 0;
    int index = startIndex + '{{mention:'.length;

    while (index < text.length - 1) {
      if (text[index] == '{' && text[index + 1] == '{') {
        braceCount += 2;
        index += 2;
      } else if (text[index] == '}' && text[index + 1] == '}') {
        if (braceCount == 0) {
          return index; // Found the matching closing braces
        } else {
          braceCount -= 2;
          index += 2;
        }
      } else {
        index++;
      }
    }

    return -1; // No matching closing braces found
  }

  /// Gets all display mentions from text
  static List<String> getAllDisplayMentions(String text) {
    List<String> encodedMentions = getAllEncodedMentions(text);
    return encodedMentions.map((encoded) => extractDisplayText(encoded)).toList();
  }

  /// Helper method to get the regex pattern for matching mentions with nested braces
  static RegExp _getMentionRegex() {
    // Use a simpler approach - find all {{mention: patterns and parse them manually
    return RegExp(r'\{\{mention:');
  }

  /// Test method to verify the new manual parser works correctly
  static void testMentionParser() {
    if (kDebugMode) {
      String testText = '{{mention:{"reference":"P174833561027848ULRX","display_text":"{{mention:{"reference":"S1721930951916","display_text":"@kitten_kreeps"}}}/digene-medicine"}}}';

      print('Testing manual parser with: $testText');

      List<String> mentions = getAllEncodedMentions(testText);

      print('Number of mentions found: ${mentions.length}');

      for (String mention in mentions) {
        print('Found mention: $mention');

        String displayText = extractDisplayText(mention);
        print('Display text: $displayText');
      }

      // Test the convertToDisplayText method
      String converted = convertToDisplayText(testText);
      print('Converted text: $converted');
    }
  }
}
