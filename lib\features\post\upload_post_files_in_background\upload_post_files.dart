import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/services/web_upload_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class PostAndCommentUploadFiles{


  //region variables
  // final BuildAppConstants.userStoreCommonBottomNavigationContext AppConstants.userStoreCommonBottomNavigationContext;
  //endregion


  //region Constructor
  PostAndCommentUploadFiles();
  //endregion

  // Web upload service instance
  final WebUploadService _webUploadService = WebUploadService();




  //region Create post api call for mobile
  Future<void> createPostApiCall({
    required String postText,
    required List<File> selectedImage,
    List<Map<String, dynamic>>? taggedReferences,
  }) async {
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);
    //If static user then open login screen
    if(CommonMethods().isStaticUser()){
      return CommonMethods().goToSignUpFlow();
    }
    try {
      postDataModel.updatePostingStatus(data: true);

      List<String> filePaths = selectedImage.map((e) => e.path).toList();
      String createdPostReference = await UploadFileService().createPost(
        filePaths: filePaths,
        postText: postText,
        storeReference: AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : null,
        userReference: AppConstants.appData.isUserView! ? AppConstants.appData.userReference : null,
        taggedReferences: taggedReferences,
      );

      await getSinglePost(postReference: createdPostReference);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.yourPostIsSent, currentContext);
      }

      postDataModel.updatePostingStatus(data: false);
    } on ApiErrorResponseMessage catch (error) {
      postDataModel.updatePostingStatus(data: false);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(error.message.toString(), currentContext);
      }
      return;
    } catch (error) {
      postDataModel.updatePostingStatus(data: false);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    }
  }
  //endregion

  //region Create post api call for web
  Future<void> createWebPostApiCall({
    required String postText,
    required List<Map<String, dynamic>> webImages,
    List<Map<String, dynamic>>? taggedReferences,
  }) async {
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);

    //If static user then open login screen
    if(CommonMethods().isStaticUser()){
      return CommonMethods().goToSignUpFlow();
    }

    try {
      postDataModel.updatePostingStatus(data: true);

      // Create a URL for the post creation API
      final url = AppConstants.createPost;

      // Create a map of form data
      final formData = {
        'post_text': postText,
        'user_reference': AppConstants.appData.isUserView! ? AppConstants.appData.userReference : "",
        'store_reference': AppConstants.appData.isStoreView! ? AppConstants.appData.storeReference : "",
      };

      // Add tagged references if available
      if (taggedReferences != null && taggedReferences.isNotEmpty) {
        formData['tagged_references_json'] = jsonEncode(taggedReferences);
      }

      // Create a WebUploadService instance
      final webUploadService = WebUploadService();

      // Upload the post with images
      String createdPostReference = await webUploadService.createPost(
        url: url,
        formData: formData,
        images: webImages,
      );

      // Get the created post
      await getSinglePost(postReference: createdPostReference);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.yourPostIsSent, currentContext);
      }

      postDataModel.updatePostingStatus(data: false);
    } on ApiErrorResponseMessage catch (error) {
      postDataModel.updatePostingStatus(data: false);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(error.message.toString(), currentContext);
      }
      return;
    } catch (error) {
      postDataModel.updatePostingStatus(data: false);

      // Use a local variable to store context to avoid BuildContext across async gaps warning
      final currentContext = AppConstants.userStoreCommonBottomNavigationContext;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    }
  }
  //endregion

  //region Edit post api call
  Future<void> editPostApiCall({
    required String postText,
    List<String>? selectedImage,
    required String postReference,
    List<Map<String, dynamic>>? taggedReferences,
  }) async {
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);

    // if (postText.trim().isEmpty) {
    //   return CommonMethods.toastMessage(AppStrings.thoughtsFieldCanNotEmpty, AppConstants.userStoreCommonBottomNavigationContext);
    // }

    try {
      postDataModel.updatePostingStatus(data: true);

      CommonMethods.toastMessage(AppStrings.yourPostIsSent, AppConstants.userStoreCommonBottomNavigationContext);

      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postReference);

      if (selectedImage != null && selectedImage.isNotEmpty) {
        await UploadFileService().editPost(
          filePaths: selectedImage,
          postText: postText,
          postReference: postReference,
          taggedReferences: taggedReferences,
        );
      } else {
        await UploadFileService().editPost(
          postText: postText,
          postReference: postReference,
          taggedReferences: taggedReferences,
        );
      }

      await getSinglePost(postReference: postReference);

      postDataModel.updatePostingStatus(data: false);
    } on ApiErrorResponseMessage catch (error) {
      postDataModel.updatePostingStatus(data: false);
      CommonMethods.toastMessage(error.message.toString(), AppConstants.userStoreCommonBottomNavigationContext);
      return;
    } catch (error) {
      postDataModel.updatePostingStatus(data: false);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
//endregion



  //region Get single post
  Future<void>getSinglePost({required String postReference})async{
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);

    try{
      //Api call
      PostDetail postDetail = await PostService().getSinglePost(postReference: postReference);
      //Add updated data into data model
      postDataModel.addPostIntoList(postList: [postDetail]);
      //Add the flag that post is adding
      postDataModel.updatePostingStatus(data: false);
    }
    on ApiErrorResponseMessage catch(error){
      //Add the flag that post is adding
      postDataModel.updatePostingStatus(data: false);
      CommonMethods.toastMessage(error.message.toString(), AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
    catch(error){
      //Add the flag that post is adding
      postDataModel.updatePostingStatus(data: false);
      //Failed
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
    }
  }
//endregion




///Comment

  //region Send comment
  Future<PostDetail> sendComment({required String parentReference,String? reviewCount, required String commentText, List<String>? selectedFiles, bool addNewCommentToDataModel = true, required CommentEnums commentEnums}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);
    try {
      // Add comment
      String commentReference = await UploadFileService().createComment(reviewCount: reviewCount,parentReference: parentReference, commentText: commentText, filePaths: selectedFiles ?? [], commentEnums: commentEnums);
      // Get added comment
      PostDetail commentApi = await PostService().getSingleComment(commentReference: commentReference);
      // Add data in post data model
      //Add new comment to data model
      postDataModel.addPostIntoList(postList: [commentApi]);

      //Now fetch same comment and add to recent comment list in data model
      postDataModel.recentlyAddedPostDetail.addAll(postDataModel.allPostDetailList.where((element) => element.postOrCommentReference == commentReference).toList());
      // Return the new comment
      return commentApi;
    }
    on ApiErrorResponseMessage catch (error) {
      AppConstants.userStoreCommonBottomNavigationContext.mounted ? CommonMethods.toastMessage(error.message.toString(), AppConstants.userStoreCommonBottomNavigationContext) : null;
      // Re-throw the error so that the caller can handle it
      // throw ApiErrorResponseMessage(message: "Zoom");
      throw error;
    }
    catch (error) {

      // AppConstants.userStoreCommonBottomNavigationContext.mounted ? CommonMethods.toastMessage(error.toString(), AppConstants.userStoreCommonBottomNavigationContext) : null;

      throw error;
    }
  }
//endregion

  //region Edit comment api call
  Future<void> editCommentApiCall({
    required String postText,
    String? reviewCount,
    List<String>? selectedImage,
    required String commentReference,
    List<Map<String, dynamic>>? taggedReferences,
  }) async {
    var postDataModel = Provider.of<PostDataModel>(AppConstants.userStoreCommonBottomNavigationContext, listen: false);

    // if (postText.trim().isEmpty) {
    //   return CommonMethods.toastMessage(AppStrings.thoughtsFieldCanNotEmpty, AppConstants.userStoreCommonBottomNavigationContext);
    // }

    try {
      // postDataModel.updatePostingStatus(data: true);

      // CommonMethods.toastMessage(AppStrings.yourCommentIsSent, AppConstants.userStoreCommonBottomNavigationContext);

      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == commentReference);

      if (selectedImage != null && selectedImage.isNotEmpty) {
        await UploadFileService().editComment(
          reviewCount: reviewCount,
          filePaths: selectedImage,
          postText: postText,
          postReference: commentReference,
          taggedReferences: taggedReferences,
        );
      } else {
        await UploadFileService().editComment(
          reviewCount: reviewCount,
          postText: postText,
          postReference: commentReference,
          taggedReferences: taggedReferences,
        );
      }

      //Get added comment
      PostDetail commentApi = await PostService().getSingleComment(commentReference: commentReference);
      //Add data in post data model
      postDataModel.addPostIntoList(postList: [commentApi]);
      postDataModel.updatePostingStatus(data: false);
    } on ApiErrorResponseMessage catch (error) {
      postDataModel.updatePostingStatus(data: false);
      CommonMethods.toastMessage(error.message.toString(), AppConstants.userStoreCommonBottomNavigationContext);
      return;
    } catch (error) {
      postDataModel.updatePostingStatus(data: false);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
//endregion
}